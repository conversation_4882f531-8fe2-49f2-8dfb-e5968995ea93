defmodule Test.Extensions do
  defmodule PrepareExtension do
    @behaviour Drops.Operations.Extension

    @impl true
    def enabled?(_opts) do
      true
    end

    @impl true
    def extend_operation(_opts) do
      quote do
        def prepare(%{params: params} = context) do
          updated_params =
            if Map.has_key?(params, :name) do
              Map.put(params, :name, "prepared_" <> params.name)
            else
              params
            end

          {:ok, Map.put(context, :params, updated_params)}
        end

        def prepare_more(%{params: params} = context) do
          {:ok, Map.put(context, :params, Map.put(params, :prepared, true))}
        end
      end
    end

    @impl true
    def extend_unit_of_work(uow, mod, _opts) do
      Drops.Operations.UnitOfWork.inject_step(uow, :prepare_more, mod, :prepare_more)
    end
  end

  defmodule ValidateExtension do
    @behaviour Drops.Operations.Extension

    @impl true
    def enabled?(_opts) do
      true
    end

    @impl true
    def extend_operation(_opts) do
      quote do
        def validate(%{params: params} = context) do
          if Map.has_key?(params, :name) and String.contains?(params.name, "invalid") do
            {:error, "name cannot contain 'invalid'"}
          else
            {:ok, context}
          end
        end
      end
    end
  end
end
